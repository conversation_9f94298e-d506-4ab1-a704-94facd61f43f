# Attendance Management System

A Flask-based attendance management system with device binding and comprehensive security features.

## Features

- **Device Binding**: Each employee can only check-in from their registered device
- **Secure Authentication**: Password hashing with bcrypt
- **Daily Check-in Limit**: One check-in per employee per day
- **Admin Dashboard**: View all attendance records with employee information
- **IP Tracking**: Records IP address for each check-in
- **Input Validation**: Comprehensive validation and sanitization

## Requirements

- Python 3.7+
- Flask 2.3.3
- passlib 1.7.4
- bcrypt 4.0.1
- python-dotenv 1.0.0

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd wifi_tracking
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Run the application:
```bash
python app.py
```

The application will start on `http://localhost:5000`

## Database Schema

### Users Table
- `id`: Primary key (auto-increment)
- `name`: Employee name
- `email`: Unique email address
- `password_hash`: Bcrypt hashed password
- `device_token`: Unique device identifier (UUID)
- `created_at`: Registration timestamp

### Attendance Table
- `id`: Primary key (auto-increment)
- `user_id`: Foreign key to users table
- `check_in_time`: Check-in timestamp
- `ip_address`: Client IP address
- `device_token`: Device token used for check-in
- Constraint: Unique(user_id, date(check_in_time)) - one check-in per day

## API Endpoints

### 1. Register Employee (IT Department)
**POST** `/register`

Register a new employee in the system.

**Request Body:**
```json
{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "SecurePass123"
}
```

**Response:**
```json
{
    "message": "User registered successfully",
    "user_id": 1
}
```

### 2. Employee Login
**POST** `/login`

Employee login with device binding on first login.

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "SecurePass123"
}
```

**Response (First Login):**
```json
{
    "message": "Device registered successfully",
    "device_token": "550e8400-e29b-41d4-a716-************",
    "user_id": 1,
    "name": "John Doe"
}
```

**Response (Subsequent Logins):**
```json
{
    "message": "Login successful",
    "device_token": "550e8400-e29b-41d4-a716-************",
    "user_id": 1,
    "name": "John Doe"
}
```

### 3. Employee Check-in
**POST** `/checkin`

Record employee attendance.

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "device_token": "550e8400-e29b-41d4-a716-************"
}
```

**Response:**
```json
{
    "message": "Check-in successful",
    "attendance_id": 1,
    "check_in_time": "2025-08-18T14:30:00.123456",
    "user_name": "John Doe"
}
```

### 4. Admin Dashboard
**GET** `/admin/attendance`

View all attendance records in a web interface.

## Security Features

1. **Password Requirements:**
   - Minimum 8 characters
   - Must contain letters and numbers
   - Hashed with bcrypt

2. **Device Token Validation:**
   - UUID format validation
   - Must match stored token for check-in
   - Generated on first login

3. **Input Validation:**
   - Email format validation
   - Input sanitization and length limits
   - SQL injection prevention

4. **Rate Limiting:**
   - One check-in per employee per day
   - Duplicate check-in prevention

## Usage Workflow

1. **IT Department Registration:**
   - IT staff registers new employees using `/register` endpoint
   - Provides name, email, and temporary password

2. **Employee First Login:**
   - Employee visits IT department for first login
   - Uses `/login` endpoint with credentials
   - Device token is generated and bound to their account

3. **Daily Check-in:**
   - Employee uses `/checkin` endpoint with email and device token
   - System validates device and records attendance
   - Only one check-in allowed per day

4. **Admin Monitoring:**
   - Admin accesses `/admin/attendance` to view all records
   - Real-time dashboard with statistics and employee data

## Error Handling

The system provides comprehensive error messages for:
- Missing required fields
- Invalid email format
- Weak passwords
- Duplicate registrations
- Invalid device tokens
- Multiple daily check-ins
- Authentication failures

## Development

To run in development mode:
```bash
export FLASK_ENV=development
python app.py
```

The application includes auto-refresh functionality in the admin dashboard and comprehensive logging for debugging.
