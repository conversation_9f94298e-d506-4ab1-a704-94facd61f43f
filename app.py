from flask import Flask, request, jsonify, render_template
import sqlite3
import uuid
from datetime import datetime, date
from passlib.hash import bcrypt
import os
import re

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')

# Database configuration
DATABASE = 'attendance.db'

# Security and validation functions
def validate_email(email):
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """Validate password strength"""
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    if not re.search(r'[A-Za-z]', password):
        return False, "Password must contain at least one letter"
    if not re.search(r'[0-9]', password):
        return False, "Password must contain at least one number"
    return True, "Password is valid"

def validate_device_token(token):
    """Validate device token format (UUID)"""
    try:
        uuid.UUID(token)
        return True
    except ValueError:
        return False

def sanitize_input(text):
    """Basic input sanitization"""
    if not text:
        return ""
    return text.strip()[:255]  # Limit length and strip whitespace

def get_db_connection():
    """Get database connection"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    """Initialize database with required tables"""
    conn = get_db_connection()

    # Create users table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            device_token TEXT UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Create attendance table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            check_in_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address TEXT NOT NULL,
            device_token TEXT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users (id),
            UNIQUE(user_id, date(check_in_time))
        )
    ''')

    conn.commit()
    conn.close()

def get_user_by_email(email):
    """Get user by email"""
    conn = get_db_connection()
    user = conn.execute('SELECT * FROM users WHERE email = ?', (email,)).fetchone()
    conn.close()
    return user

def get_user_by_id(user_id):
    """Get user by ID"""
    conn = get_db_connection()
    user = conn.execute('SELECT * FROM users WHERE id = ?', (user_id,)).fetchone()
    conn.close()
    return user

def create_user(name, email, password):
    """Create a new user"""
    conn = get_db_connection()
    password_hash = bcrypt.hash(password)

    try:
        cursor = conn.execute(
            'INSERT INTO users (name, email, password_hash) VALUES (?, ?, ?)',
            (name, email, password_hash)
        )
        user_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return user_id
    except sqlite3.IntegrityError:
        conn.close()
        return None

def update_device_token(user_id, device_token):
    """Update user's device token"""
    conn = get_db_connection()
    conn.execute(
        'UPDATE users SET device_token = ? WHERE id = ?',
        (device_token, user_id)
    )
    conn.commit()
    conn.close()

def check_daily_attendance(user_id, check_date):
    """Check if user already checked in today"""
    conn = get_db_connection()
    attendance = conn.execute(
        'SELECT * FROM attendance WHERE user_id = ? AND date(check_in_time) = ?',
        (user_id, check_date)
    ).fetchone()
    conn.close()
    return attendance is not None

def create_attendance_record(user_id, ip_address, device_token):
    """Create attendance record"""
    conn = get_db_connection()
    try:
        cursor = conn.execute(
            'INSERT INTO attendance (user_id, ip_address, device_token) VALUES (?, ?, ?)',
            (user_id, ip_address, device_token)
        )
        attendance_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return attendance_id
    except sqlite3.IntegrityError:
        conn.close()
        return None

# API Endpoints

@app.route('/register', methods=['POST'])
def register():
    """Register new employee (IT department use)"""
    data = request.get_json()

    if not data or not all(k in data for k in ('name', 'email', 'password')):
        return jsonify({'error': 'Missing required fields: name, email, password'}), 400

    name = sanitize_input(data['name'])
    email = sanitize_input(data['email']).lower()
    password = data['password']

    if not name or not email or not password:
        return jsonify({'error': 'All fields are required'}), 400

    # Validate email format
    if not validate_email(email):
        return jsonify({'error': 'Invalid email format'}), 400

    # Validate password strength
    is_valid, message = validate_password(password)
    if not is_valid:
        return jsonify({'error': message}), 400

    # Check if user already exists
    if get_user_by_email(email):
        return jsonify({'error': 'User with this email already exists'}), 409

    # Create user
    user_id = create_user(name, email, password)
    if user_id:
        return jsonify({
            'message': 'User registered successfully',
            'user_id': user_id
        }), 201
    else:
        return jsonify({'error': 'Failed to register user'}), 500

@app.route('/login', methods=['POST'])
def login():
    """Employee login with device binding"""
    data = request.get_json()

    if not data or not all(k in data for k in ('email', 'password')):
        return jsonify({'error': 'Missing required fields: email, password'}), 400

    email = sanitize_input(data['email']).lower()
    password = data['password']

    # Validate email format
    if not validate_email(email):
        return jsonify({'error': 'Invalid email format'}), 400

    # Get user
    user = get_user_by_email(email)
    if not user or not bcrypt.verify(password, user['password_hash']):
        return jsonify({'error': 'Invalid email or password'}), 401

    # Check if device token exists (first time login)
    if not user['device_token']:
        # Generate new device token
        device_token = str(uuid.uuid4())
        update_device_token(user['id'], device_token)

        return jsonify({
            'message': 'Device registered successfully',
            'device_token': device_token,
            'user_id': user['id'],
            'name': user['name']
        }), 200
    else:
        # Return existing device token
        return jsonify({
            'message': 'Login successful',
            'device_token': user['device_token'],
            'user_id': user['id'],
            'name': user['name']
        }), 200

@app.route('/checkin', methods=['POST'])
def checkin():
    """Employee check-in with device validation"""
    data = request.get_json()

    if not data or not all(k in data for k in ('email', 'device_token')):
        return jsonify({'error': 'Missing required fields: email, device_token'}), 400

    email = sanitize_input(data['email']).lower()
    device_token = sanitize_input(data['device_token'])

    # Validate email format
    if not validate_email(email):
        return jsonify({'error': 'Invalid email format'}), 400

    # Validate device token format
    if not validate_device_token(device_token):
        return jsonify({'error': 'Invalid device token format'}), 400

    # Get user
    user = get_user_by_email(email)
    if not user:
        return jsonify({'error': 'User not found'}), 404

    # Check if user has a device token (must login first)
    if not user['device_token']:
        return jsonify({'error': 'Device not registered. Please login first.'}), 403

    # Validate device token
    if user['device_token'] != device_token:
        return jsonify({'error': 'Invalid device token. Please contact IT department.'}), 403

    # Check if already checked in today
    today = date.today().isoformat()
    if check_daily_attendance(user['id'], today):
        return jsonify({'error': 'You have already checked in today'}), 409

    # Get client IP address (handle proxy headers)
    ip_address = request.headers.get('X-Forwarded-For',
                                   request.headers.get('X-Real-IP',
                                   request.remote_addr or 'unknown'))
    if ',' in ip_address:
        ip_address = ip_address.split(',')[0].strip()

    # Create attendance record
    attendance_id = create_attendance_record(user['id'], ip_address, device_token)
    if attendance_id:
        return jsonify({
            'message': 'Check-in successful',
            'attendance_id': attendance_id,
            'check_in_time': datetime.now().isoformat(),
            'user_name': user['name']
        }), 201
    else:
        return jsonify({'error': 'Failed to record attendance'}), 500

@app.route('/admin/attendance', methods=['GET'])
def admin_attendance():
    """Admin page to view all attendance records"""
    conn = get_db_connection()

    # Get all attendance records with user information
    attendance_records = conn.execute('''
        SELECT
            a.id,
            u.name,
            u.email,
            a.check_in_time,
            a.ip_address,
            a.device_token
        FROM attendance a
        JOIN users u ON a.user_id = u.id
        ORDER BY a.check_in_time DESC
    ''').fetchall()

    # Get today's check-ins count
    today = date.today().isoformat()
    today_count = conn.execute('''
        SELECT COUNT(*) as count
        FROM attendance
        WHERE date(check_in_time) = ?
    ''', (today,)).fetchone()['count']

    # Get unique employees count
    unique_employees = conn.execute('''
        SELECT COUNT(DISTINCT user_id) as count
        FROM attendance
    ''').fetchone()['count']

    conn.close()

    return render_template('admin_attendance.html',
                         records=attendance_records,
                         today_count=today_count,
                         unique_employees=unique_employees)

if __name__ == '__main__':
    init_db()
    app.run(debug=True, host='0.0.0.0', port=5000)
