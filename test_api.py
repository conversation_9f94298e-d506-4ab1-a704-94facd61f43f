#!/usr/bin/env python3
"""
Test script for the Attendance Management System API
Run this script to test all endpoints and functionality
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:5000"
TEST_USER = {
    "name": "Test Employee",
    "email": "<EMAIL>",
    "password": "TestPass123"
}

def test_register():
    """Test user registration"""
    print("Testing user registration...")
    
    url = f"{BASE_URL}/register"
    response = requests.post(url, json=TEST_USER)
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    
    if response.status_code == 201:
        print("✅ Registration successful")
        return True
    elif response.status_code == 409:
        print("⚠️  User already exists")
        return True
    else:
        print("❌ Registration failed")
        return False

def test_login():
    """Test user login"""
    print("\nTesting user login...")
    
    url = f"{BASE_URL}/login"
    login_data = {
        "email": TEST_USER["email"],
        "password": TEST_USER["password"]
    }
    
    response = requests.post(url, json=login_data)
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    
    if response.status_code == 200:
        print("✅ Login successful")
        return response.json().get("device_token")
    else:
        print("❌ Login failed")
        return None

def test_checkin(device_token):
    """Test check-in functionality"""
    print("\nTesting check-in...")
    
    url = f"{BASE_URL}/checkin"
    checkin_data = {
        "email": TEST_USER["email"],
        "device_token": device_token
    }
    
    response = requests.post(url, json=checkin_data)
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    
    if response.status_code == 201:
        print("✅ Check-in successful")
        return True
    elif response.status_code == 409:
        print("⚠️  Already checked in today")
        return True
    else:
        print("❌ Check-in failed")
        return False

def test_duplicate_checkin(device_token):
    """Test duplicate check-in prevention"""
    print("\nTesting duplicate check-in prevention...")
    
    url = f"{BASE_URL}/checkin"
    checkin_data = {
        "email": TEST_USER["email"],
        "device_token": device_token
    }
    
    response = requests.post(url, json=checkin_data)
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    
    if response.status_code == 409:
        print("✅ Duplicate check-in correctly prevented")
        return True
    else:
        print("❌ Duplicate check-in prevention failed")
        return False

def test_invalid_device_token():
    """Test invalid device token"""
    print("\nTesting invalid device token...")
    
    url = f"{BASE_URL}/checkin"
    checkin_data = {
        "email": TEST_USER["email"],
        "device_token": "invalid-token-123"
    }
    
    response = requests.post(url, json=checkin_data)
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    
    if response.status_code == 400:
        print("✅ Invalid device token correctly rejected")
        return True
    else:
        print("❌ Invalid device token validation failed")
        return False

def test_admin_page():
    """Test admin page accessibility"""
    print("\nTesting admin page...")
    
    url = f"{BASE_URL}/admin/attendance"
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Admin page accessible")
            print(f"Page content length: {len(response.text)} characters")
            return True
        else:
            print("❌ Admin page not accessible")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Attendance Management System API Tests")
    print("=" * 60)
    
    # Test if server is running
    try:
        response = requests.get(f"{BASE_URL}/admin/attendance")
        print("✅ Server is running")
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running. Please start the Flask app first.")
        print("Run: python app.py")
        return
    
    # Run tests
    tests_passed = 0
    total_tests = 6
    
    if test_register():
        tests_passed += 1
    
    device_token = test_login()
    if device_token:
        tests_passed += 1
        
        if test_checkin(device_token):
            tests_passed += 1
        
        if test_duplicate_checkin(device_token):
            tests_passed += 1
    
    if test_invalid_device_token():
        tests_passed += 1
    
    if test_admin_page():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print(f"🏁 Test Summary: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! The system is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
