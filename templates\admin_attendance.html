<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Management - Admin Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 6px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .device-token {
            font-family: monospace;
            font-size: 0.9em;
            color: #666;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .no-records {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }
        .refresh-btn {
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .refresh-btn:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Attendance Management System - Admin Dashboard</h1>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">{{ records|length }}</div>
                <div class="stat-label">Total Check-ins</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ unique_employees }}</div>
                <div class="stat-label">Unique Employees</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ today_count }}</div>
                <div class="stat-label">Today's Check-ins</div>
            </div>
        </div>

        <button class="refresh-btn" onclick="window.location.reload()">Refresh Data</button>

        {% if records %}
        <table>
            <thead>
                <tr>
                    <th>Employee Name</th>
                    <th>Email</th>
                    <th>Check-in Date</th>
                    <th>Check-in Time</th>
                    <th>IP Address</th>
                    <th>Device Token</th>
                </tr>
            </thead>
            <tbody>
                {% for record in records %}
                <tr>
                    <td>{{ record.name }}</td>
                    <td>{{ record.email }}</td>
                    <td>{{ record.check_in_time[:10] }}</td>
                    <td>{{ record.check_in_time[11:19] }}</td>
                    <td>{{ record.ip_address }}</td>
                    <td class="device-token" title="{{ record.device_token }}">{{ record.device_token }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div class="no-records">
            <p>No attendance records found.</p>
            <p>Employees need to check in first to see records here.</p>
        </div>
        {% endif %}
    </div>

    <script>
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>
