#!/usr/bin/env python3
"""
Installation script for Attendance Management System
This script sets up the environment and initializes the database
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install requirements")
        return False

def create_env_file():
    """Create .env file if it doesn't exist"""
    if not os.path.exists('.env'):
        print("Creating .env file...")
        try:
            with open('.env.example', 'r') as example:
                content = example.read()
            
            with open('.env', 'w') as env_file:
                env_file.write(content)
            
            print("✅ .env file created from example")
            print("⚠️  Please update the SECRET_KEY in .env file for production use")
            return True
        except Exception as e:
            print(f"❌ Failed to create .env file: {e}")
            return False
    else:
        print("✅ .env file already exists")
        return True

def initialize_database():
    """Initialize the database"""
    print("Initializing database...")
    try:
        # Import and run the initialization
        from app import init_db
        init_db()
        print("✅ Database initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to initialize database: {e}")
        return False

def main():
    """Main installation process"""
    print("🚀 Attendance Management System Installation")
    print("=" * 50)
    
    success_count = 0
    total_steps = 3
    
    # Step 1: Install requirements
    if install_requirements():
        success_count += 1
    
    # Step 2: Create environment file
    if create_env_file():
        success_count += 1
    
    # Step 3: Initialize database
    if initialize_database():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"Installation Summary: {success_count}/{total_steps} steps completed")
    
    if success_count == total_steps:
        print("🎉 Installation completed successfully!")
        print("\nNext steps:")
        print("1. Update the SECRET_KEY in .env file for production")
        print("2. Run the application: python app.py")
        print("3. Test the API: python test_api.py")
        print("4. Access admin dashboard: http://localhost:5000/admin/attendance")
    else:
        print("⚠️  Installation completed with some issues")
        print("Please check the error messages above and resolve them")

if __name__ == "__main__":
    main()
